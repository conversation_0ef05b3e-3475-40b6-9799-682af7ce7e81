<template>
    <div class="ai-agent">
        <div class="ai-agent__switch">
            <el-switch
                v-model="enableAgent"
                :disabled="loading"
                @change="handleSwitchChange"
            ></el-switch>
            <span>拖章Agent</span>
        </div>
        <div class="ai-agent__description">
            <p>凡是从本地上传的文档，Agent都会自动帮你找到各个签约方的盖章处，以代替人工施动盖章处的繁琐操作</p>
            <p>*单份文档页数不能超过50页</p>
            <p>*配合"模板专用章"、"自动盖"等功能，可以实现非标合同盖章的完全自动化</p>
        </div>
        <template v-if="enableAgent">
            <div class="ai-agent__config" v-loading="loading">
                <h3>拖章规则配置</h3>
                <p class="config-tip">勾选需要使用的规则，并调整各个规则的排序，以明确规则冲突时的优先级。</p>

                <draggable
                    v-model="ruleList"
                    :disabled="!enableAgent"
                    handle=".drag-handle"
                    ghost-class="ghost"
                    @end="onDragEnd"
                >
                    <div
                        v-for="(rule, index) in ruleList"
                        :key="rule.strategy"
                        class="rule-item"
                        :class="{ 'disabled': !enableAgent }"
                    >
                        <div class="rule-header">
                            <div class="rule-content">
                                <i class="drag-handle el-icon-rank" :class="{ 'disabled': !enableAgent }"></i>
                                <span class="rule-order">{{ index + 1 }}.</span>
                                <el-checkbox
                                    v-model="rule.isOpen"
                                    :disabled="!enableAgent"
                                    @change="updateRuleEnabled(rule)"
                                >
                                    {{ rule.label }}
                                </el-checkbox>
                            </div>
                        </div>
                        <p class="rule-desc">{{ rule.description }}</p>
                    </div>
                </draggable>
            </div>

            <div class="ai-agent__adjustment">
                <h3>自助调优</h3>
                <div class="adjustment-item">
                    <p>(1) 盖章位置调优（不含骑缝章）</p>
                    <div class="position-adjust">
                        <div class="adjust-row">
                            <span>系统指定的盖章处需向上移动</span>
                            <el-input v-model="sealMoveConfig.upMove" size="small" class="adjust-input" :disabled="!enableAgent"></el-input>
                            <span>厘米</span>
                        </div>
                        <div class="adjust-row">
                            <span>系统指定的盖章处需向左移动</span>
                            <el-input v-model="sealMoveConfig.leftMove" size="small" class="adjust-input" :disabled="!enableAgent"></el-input>
                            <span>厘米</span>
                        </div>
                    </div>
                    <p class="adjust-tip">*可填写负数，代表反方向。</p>
                    <p class="adjust-tip">*N厘米是打印后的大小，默认情况下一个章的直径约4厘米，可以以此折算</p>
                    <p class="adjust-tip">*移动盖章处可能会与已有盖章处重叠，请知悉</p>
                </div>
                
                <div class="adjustment-item">
                    <p>(2) 是否盖章调整</p>
                    <div class="content-adjust">
                        <div class="adjust-row">
                            <span>若文件中出现</span>
                            <el-input v-model="addSealByKeyword.keyword" size="small" class="content-input" :disabled="!enableAgent"></el-input>
                            <span>该内容对应的位置需要盖章</span>
                        </div>
                        <div class="adjust-row">
                            <span>若文件中出现</span>
                            <el-input v-model="removeSealByKeyword.keyword" size="small" class="content-input" :disabled="!enableAgent"></el-input>
                            <span>该内容对应的位置不需要盖章</span>
                        </div>
                    </div>
                    <p class="adjust-tip">*此处配置后，规则将变成最高优先级</p>
                </div>
            </div>

            <div class="ai-agent__actions">
                <el-button
                    type="primary"
                    :loading="loading"
                    :disabled="!enableAgent"
                    @click="saveConfig"
                >
                    保存配置
                </el-button>
                <el-button @click="resetConfig">重置配置</el-button>
            </div>
        </template>
    </div>
</template>

<script>
import { getStampRecommendationRule, saveStampRecommendationRule, toggleStampRecommendationSwitch } from 'src/api/template/index.js';
import { mapGetters } from 'vuex';
import draggable from 'vuedraggable';

export default {
    name: 'AIAgent',
    components: {
        draggable
    },
    props: {
        templateId: {
            type: String,
            required: true
        },
        templateName: {
            type: String,
            default: ''
        }
    },
    computed: {
        ...mapGetters([
            'checkAdvancedFeatureData'
        ]),
        // 检查拖章功能是否可用
        isStampRecommendationAvailable() {
            // 假设拖章功能的 featureId 是 '241'，如果实际不是这个ID，需要替换为正确的ID
            const featureData = this.checkAdvancedFeatureData('244');
            return featureData.isOpen || false;
        }
    },
    data() {
        return {
            enableAgent: false,
            loading: false,
            // 规则列表，支持排序
            ruleList: [
                {
                    strategy: 'NEED_OVERLAPPING_SEAL',
                    label: '需要骑缝章',
                    description: '*如果不勾选，则不会自动添加骑缝章盖章处',
                    isOpen: false,
                    order: 1
                },
                {
                    strategy: 'RECIPROCAL_SEALING',
                    label: '对等盖章：按已有盖章处匹配签约方的盖章处',
                    description: '*例如，若合同上已有对方的3个印章（分布在不同页面），系统会自动为我方在相应位置也生成3个对应的盖章位置。 注：本功能仅在单方盖章时生效。',
                    isOpen: false,
                    order: 2
                },
                {
                    strategy: 'SEAL_ON_LAST_LINE_OF_TEXT',
                    label: '文件中若没有明确的盖章处，则盖在文件最后一行的文字上',
                    description: '*不是传统意义上的合同，没有明确的盖章处指示的文件，如内部单据等需要盖章的文件',
                    isOpen: true, // 默认勾选
                    order: 3
                },
                {
                    strategy: 'REASONABLE_AREA_SEALING',
                    label: '印章应放置在合理的区域（如盖章栏附近、合同末页公司信息处等）',
                    description: '*可能会与对等盖章、每页盖章等规则有冲突，需要调整优先级，以确保优先需要满足的规则',
                    isOpen: true, // 默认勾选
                    order: 4
                },
                {
                    strategy: 'SEAL_EACH_PAGE',
                    label: '每页盖章',
                    description: '*如果是对账单、招投标文件，则每页相同位置都需要盖章',
                    isOpen: false,
                    order: 5
                }
            ],
            // 位置调优配置
            sealMoveConfig: {
                enabled: false,
                upMove: '0',
                leftMove: '0'
            },
            // 关键字配置
            addSealByKeyword: {
                enabled: false,
                keyword: ''
            },
            removeSealByKeyword: {
                enabled: false,
                keyword: ''
            }
        };
    },
    mounted() {
        this.loadConfig();
    },
    watch: {
        // 监听位置调优输入，自动设置enabled状态
        'sealMoveConfig.upMove'() {
            this.updateMoveConfigEnabled();
        },
        'sealMoveConfig.leftMove'() {
            this.updateMoveConfigEnabled();
        },
        // 监听关键字输入，自动设置enabled状态
        'addSealByKeyword.keyword'() {
            this.addSealByKeyword.enabled = !!this.addSealByKeyword.keyword.trim();
        },
        'removeSealByKeyword.keyword'() {
            this.removeSealByKeyword.enabled = !!this.removeSealByKeyword.keyword.trim();
        }
    },
    methods: {
        // 拖拽结束事件
        onDragEnd() {
            // 更新排序
            this.ruleList.forEach((rule, index) => {
                rule.order = index + 1;
            });
        },

        // 更新规则启用状态
        updateRuleEnabled(rule) {
            // 可以在这里添加额外的逻辑
            console.log(`规则 ${rule.strategy} 状态变更为: ${rule.isOpen}`);
        },

        // 加载配置
        async loadConfig() {
            if (!this.templateId) return;

            this.loading = true;
            try {
                const response = await getStampRecommendationRule(this.templateId);
                const data = response.data;

                // 检查功能是否可用，如果不可用则强制关闭
                // if (!this.isStampRecommendationAvailable) {
                //     this.enableAgent = false;
                // } else {
                //     // 设置开关状态
                    this.enableAgent = data.useStampRecommendation || false;
                // }

                // 解析策略配置
                if (data.stampStrategies && Array.isArray(data.stampStrategies)) {
                    this.parseStrategiesFromAPI(data.stampStrategies);
                }
            } catch (error) {
                console.error('加载印章推荐配置失败:', error);
                this.$message.error('加载配置失败，请稍后重试');
            } finally {
                this.loading = false;
            }
        },

        // 解析API返回的策略配置
        parseStrategiesFromAPI(strategies) {
            // 重置所有配置
            this.resetConfig();

            // 创建策略映射，按order排序
            const strategyMap = {};
            strategies.forEach(strategy => {
                strategyMap[strategy.strategy] = strategy;
            });

            // 更新规则列表
            this.ruleList.forEach(rule => {
                const apiStrategy = strategyMap[rule.strategy];
                if (apiStrategy) {
                    const isOpen = apiStrategy.strategyConfigParam?.isOpen;
                    rule.isOpen = isOpen !== undefined ? isOpen : true;
                    rule.order = apiStrategy.order || rule.order;
                }
            });

            // 按order排序
            this.ruleList.sort((a, b) => {
                const aOrder = strategyMap[a.strategy]?.order || a.order;
                const bOrder = strategyMap[b.strategy]?.order || b.order;
                return aOrder - bOrder;
            });

            // 处理特殊配置
            strategies.forEach(strategy => {
                const { strategy: strategyType, strategyConfigParam } = strategy;

                switch (strategyType) {
                    case 'SEAL_MOVE_CONFIG':
                        const sealMoveIsOpen = strategyConfigParam?.isOpen;
                        this.sealMoveConfig.enabled = sealMoveIsOpen !== undefined ? sealMoveIsOpen : true;
                        if (strategyConfigParam) {
                            this.sealMoveConfig.upMove = strategyConfigParam.upMove || '0';
                            this.sealMoveConfig.leftMove = strategyConfigParam.leftMove || '0';
                        }
                        break;
                    case 'ADD_SEAL_BY_KEYWORD':
                        const addKeywordIsOpen = strategyConfigParam?.isOpen;
                        this.addSealByKeyword.enabled = addKeywordIsOpen !== undefined ? addKeywordIsOpen : true;
                        if (strategyConfigParam) {
                            this.addSealByKeyword.keyword = strategyConfigParam.keyword || '';
                        }
                        break;
                    case 'REMOVE_SEAL_BY_KEYWORD':
                        const removeKeywordIsOpen = strategyConfigParam?.isOpen;
                        this.removeSealByKeyword.enabled = removeKeywordIsOpen !== undefined ? removeKeywordIsOpen : true;
                        if (strategyConfigParam) {
                            this.removeSealByKeyword.keyword = strategyConfigParam.keyword || '';
                        }
                        break;
                }
            });
        },

        // 重置配置
        resetConfig() {
            // 重置规则列表，保持默认勾选项
            this.ruleList.forEach(rule => {
                // 只有默认勾选的项保持勾选状态
                if (rule.strategy === 'SEAL_ON_LAST_LINE_OF_TEXT' ||
                    rule.strategy === 'REASONABLE_AREA_SEALING') {
                    rule.isOpen = true;
                } else {
                    rule.isOpen = false;
                }
            });

            // 重置特殊配置
            this.sealMoveConfig = {
                enabled: false,
                upMove: '0',
                leftMove: '0'
            };
            this.addSealByKeyword = {
                enabled: false,
                keyword: ''
            };
            this.removeSealByKeyword = {
                enabled: false,
                keyword: ''
            };
        },

        // 保存配置
        async saveConfig() {
            if (!this.templateId) {
                this.$message.error('模板ID不能为空');
                return;
            }

            this.loading = true;
            try {
                const ruleConfig = this.buildRuleConfig();
                await saveStampRecommendationRule(this.templateId, ruleConfig);
                this.$message.success('保存成功');
            } catch (error) {
                console.error('保存印章推荐配置失败:', error);
                this.$message.error('保存失败，请稍后重试');
            } finally {
                this.loading = false;
            }
        },

        // 构建规则配置对象
        buildRuleConfig() {
            const stampStrategies = [];
            let currentOrder = 1;

            // 添加所有规则策略，按排序顺序（包括未启用的）
            this.ruleList.forEach((rule, index) => {
                stampStrategies.push({
                    order: index + 1,
                    strategy: rule.strategyType,
                    strategyConfigParam: {
                        isOpen: rule.enabled
                    }
                });
            });

            // 更新当前排序位置
            currentOrder = this.ruleList.length + 1;

            // 添加位置调优配置
            if (this.sealMoveConfig.enabled) {
                const upMove = parseFloat(this.sealMoveConfig.upMove) || 0;
                const leftMove = parseFloat(this.sealMoveConfig.leftMove) || 0;
                if (upMove !== 0 || leftMove !== 0) {
                    stampStrategies.push({
                        order: currentOrder++,
                        strategy: 'SEAL_MOVE_CONFIG',
                        strategyConfigParam: {
                            isOpen: true,
                            upMove: upMove.toString(),
                            leftMove: leftMove.toString()
                        }
                    });
                }
            }

            // 添加关键字配置
            if (this.addSealByKeyword.enabled && this.addSealByKeyword.keyword.trim()) {
                stampStrategies.push({
                    order: currentOrder++,
                    strategy: 'ADD_SEAL_BY_KEYWORD',
                    strategyConfigParam: {
                        isOpen: true,
                        keyword: this.addSealByKeyword.keyword.trim()
                    }
                });
            }

            if (this.removeSealByKeyword.enabled && this.removeSealByKeyword.keyword.trim()) {
                stampStrategies.push({
                    order: currentOrder++,
                    strategy: 'REMOVE_SEAL_BY_KEYWORD',
                    strategyConfigParam: {
                        isOpen: true,
                        keyword: this.removeSealByKeyword.keyword.trim()
                    }
                });
            }

            return {
                useStampRecommendation: this.enableAgent,
                stampStrategies
            };
        },

        // 更新位置调优enabled状态
        updateMoveConfigEnabled() {
            const upMove = parseFloat(this.sealMoveConfig.upMove) || 0;
            const leftMove = parseFloat(this.sealMoveConfig.leftMove) || 0;
            this.sealMoveConfig.enabled = upMove !== 0 || leftMove !== 0;
        },

        // 处理开关切换
        async handleSwitchChange(value) {
            // 检查功能是否可用
            // if (value && !this.isStampRecommendationAvailable) {
            //     this.$message.error('需联系上上签支持人员付费开启');
            //     this.enableAgent = false; // 强制关闭
            //     return;
            // }

            this.loading = true;
            try {
                await toggleStampRecommendationSwitch(this.templateId, value);
                this.$message.success(value ? '已开启印章推荐功能' : '已关闭印章推荐功能');
            } catch (error) {
                console.error('切换印章推荐开关失败:', error);
                this.$message.error('操作失败，请稍后重试');
                this.enableAgent = !value; // 回滚状态
            } finally {
                this.loading = false;
            }
        }
    }
};
</script>

<style lang="scss">
.ai-agent {
    padding: 20px;
    
    &__switch {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        span {
            margin-left: 10px;
            font-size: 14px;
        }

        .feature-tip {
            color: #999;
            font-size: 12px;
        }
    }
    
    &__description {
        background-color: #f8f8f8;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 20px;
        
        p {
            margin: 5px 0;
            font-size: 13px;
            line-height: 1.5;
        }
    }
    
    &__config {
        margin-bottom: 30px;
        
        h3 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .config-tip {
            font-size: 13px;
            color: #666;
            margin-bottom: 15px;
        }
    }
    
    .rule-item {
        margin-bottom: 15px;
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 4px;
        background-color: #fff;
        transition: all 0.3s ease;

        &:hover {
            border-color: #ddd;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        &.disabled {
            background-color: #f5f5f5;
            opacity: 0.6;
        }

        .rule-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .rule-content {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .drag-handle {
            cursor: move;
            margin-right: 8px;
            color: #999;
            font-size: 16px;

            &.disabled {
                cursor: not-allowed;
                color: #ccc;
            }

            &:hover:not(.disabled) {
                color: #666;
            }
        }

        .rule-order {
            margin-right: 8px;
            font-weight: bold;
            color: #666;
            min-width: 20px;
        }

        .rule-desc {
            margin-top: 8px;
            margin-left: 32px;
            font-size: 12px;
            color: #999;
            line-height: 1.4;
        }
    }

    .ghost {
        opacity: 0.5;
        background-color: #f0f0f0;
    }
    
    &__adjustment {
        h3 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .adjustment-item {
            margin-bottom: 20px;
            
            p {
                margin-bottom: 10px;
            }
            
            .position-adjust, .content-adjust {
                margin-left: 20px;
                margin-bottom: 10px;
            }
            
            .adjust-row {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                
                span {
                    font-size: 13px;
                }
            }
            
            .adjust-input {
                width: 80px;
                margin: 0 10px;
            }
            
            .content-input {
                width: 300px;
                margin: 0 10px;
            }
            
            .adjust-tip {
                font-size: 12px;
                color: #999;
                margin-left: 20px;
            }
        }
    }

    &__actions {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
        text-align: center;

        .el-button {
            margin: 0 10px;
        }
    }
}
</style>